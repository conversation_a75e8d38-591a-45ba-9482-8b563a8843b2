// OAuth Configuration
export const OAUTH_CONFIG = {
  google: {
    clientId: process.env.VITE_GOOGLE_CLIENT_ID || '',
    redirectUri: `${window.location.origin}/auth/google/callback`,
    scope: 'openid email profile',
  },
  facebook: {
    appId: process.env.VITE_FACEBOOK_APP_ID || '',
    redirectUri: `${window.location.origin}/auth/facebook/callback`,
    scope: 'email',
  },
};

// Google OAuth URL generator
export const getGoogleAuthUrl = () => {
  const params = new URLSearchParams({
    client_id: OAUTH_CONFIG.google.clientId,
    redirect_uri: OAUTH_CONFIG.google.redirectUri,
    response_type: 'code',
    scope: OAUTH_CONFIG.google.scope,
    access_type: 'offline',
    prompt: 'consent',
  });

  return `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`;
};

// Facebook OAuth URL generator
export const getFacebookAuthUrl = () => {
  const params = new URLSearchParams({
    client_id: OAUTH_CONFIG.facebook.appId,
    redirect_uri: OAUTH_CONFIG.facebook.redirectUri,
    response_type: 'code',
    scope: OAUTH_CONFIG.facebook.scope,
  });

  return `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;
};

// OAuth handlers
export const handleGoogleAuth = () => {
  if (!OAUTH_CONFIG.google.clientId) {
    console.error('Google Client ID not configured');
    return;
  }
  window.location.href = getGoogleAuthUrl();
};

export const handleFacebookAuth = () => {
  if (!OAUTH_CONFIG.facebook.appId) {
    console.error('Facebook App ID not configured');
    return;
  }
  window.location.href = getFacebookAuthUrl();
};

// Email validation utility
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password strength validation
export const validatePasswordStrength = (password: string) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const errors: string[] = [];

  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  if (!hasUpperCase) {
    errors.push('Password must contain at least one uppercase letter');
  }
  if (!hasLowerCase) {
    errors.push('Password must contain at least one lowercase letter');
  }
  if (!hasNumbers) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength: {
      hasMinLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
    },
  };
};

// Local storage utilities for auth state
export const AUTH_STORAGE_KEYS = {
  TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER: 'user_data',
} as const;

export const setAuthToken = (token: string) => {
  localStorage.setItem(AUTH_STORAGE_KEYS.TOKEN, token);
};

export const getAuthToken = (): string | null => {
  return localStorage.getItem(AUTH_STORAGE_KEYS.TOKEN);
};

export const removeAuthToken = () => {
  localStorage.removeItem(AUTH_STORAGE_KEYS.TOKEN);
  localStorage.removeItem(AUTH_STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(AUTH_STORAGE_KEYS.USER);
};

export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};