import { 
  Box, 
  Text, 
  Group, 
  Avatar, 
  Button, 
  TextInput, 
  UnstyledButton,
  Tabs,
  Badge,
  ActionIcon
} from "@mantine/core";
import { 
  HiSearch, 
  HiBell, 
  HiMail, 
  HiShare, 
  HiStar,
  HiChevronLeft,
  HiPlus
} from "react-icons/hi";
import { css } from "styled-system/css";

interface HeaderProps {
  title?: string;
  breadcrumbs?: string[];
}

const teamMembers = [
  { name: "<PERSON>", avatar: null, color: "#3B82F6" },
  { name: "<PERSON>", avatar: null, color: "#10B981" },
  { name: "<PERSON>", avatar: null, color: "#F59E0B" },
  { name: "<PERSON>", avatar: null, color: "#8B5CF6" },
];

export function Header({ title = "Unifine Design System", breadcrumbs = ["Project", "NovaFlow Design System", "Board"] }: HeaderProps) {
  return (
    <Box
      className={css({
        backgroundColor: "white",
        borderBottom: "1px solid #E5E7EB",
        padding: "0",
      })}
    >
      {/* Top Header */}
      <Box
        className={css({
          padding: "12px 24px",
          borderBottom: "1px solid #F3F4F6",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        })}
      >
        {/* Breadcrumbs */}
        <Group gap="xs" align="center">
          <UnstyledButton
            className={css({
              padding: "4px",
              borderRadius: "4px",
              color: "#6B7280",
              "&:hover": {
                backgroundColor: "#F3F4F6",
              },
            })}
          >
            <HiChevronLeft size={16} />
          </UnstyledButton>
          
          {breadcrumbs.map((crumb, index) => (
            <Group key={index} gap="xs" align="center">
              <Text 
                size="sm" 
                c={index === breadcrumbs.length - 1 ? "#1F2937" : "#6B7280"}
                fw={index === breadcrumbs.length - 1 ? 500 : 400}
              >
                {crumb}
              </Text>
              {index < breadcrumbs.length - 1 && (
                <Text size="sm" c="#D1D5DB">/</Text>
              )}
            </Group>
          ))}
        </Group>

        {/* Right Side */}
        <Group gap="md">
          <TextInput
            placeholder="Search"
            leftSection={<HiSearch size={16} />}
            className={css({
              "& input": {
                backgroundColor: "#F9FAFB",
                border: "1px solid #E5E7EB",
                borderRadius: "8px",
                fontSize: "14px",
                "&:focus": {
                  borderColor: "#3B82F6",
                  backgroundColor: "white",
                },
              },
            })}
            w={240}
          />
          
          <ActionIcon variant="subtle" color="gray">
            <HiBell size={18} />
          </ActionIcon>
          
          <ActionIcon variant="subtle" color="gray">
            <HiMail size={18} />
          </ActionIcon>
          
          <Avatar size="sm" color="blue">
            <Text size="xs">JD</Text>
          </Avatar>
        </Group>
      </Box>

      {/* Project Header */}
      <Box
        className={css({
          padding: "20px 24px 16px",
        })}
      >
        <Group justify="space-between" align="flex-start" mb="lg">
          <Group gap="md" align="center">
            <Text 
              size="xl" 
              fw={600} 
              c="#1F2937"
              className={css({ fontSize: "24px" })}
            >
              {title}
            </Text>
            
            <Group gap="xs">
              <Button
                variant="subtle"
                size="sm"
                leftSection={<HiShare size={14} />}
                className={css({
                  color: "#6B7280",
                  fontSize: "13px",
                  "&:hover": {
                    backgroundColor: "#F3F4F6",
                  },
                })}
              >
                Share
              </Button>
              
              <ActionIcon variant="subtle" color="gray" size="sm">
                <HiStar size={16} />
              </ActionIcon>
            </Group>
          </Group>

          <Group gap="md">
            {/* Team Members */}
            <Group gap="xs">
              <Avatar.Group spacing="xs">
                {teamMembers.map((member, index) => (
                  <Avatar 
                    key={index} 
                    size="sm" 
                    color={member.color}
                    className={css({
                      border: "2px solid white",
                    })}
                  >
                    <Text size="xs">{member.name.split(' ').map(n => n[0]).join('')}</Text>
                  </Avatar>
                ))}
              </Avatar.Group>
              
              <Button
                variant="subtle"
                size="sm"
                leftSection={<HiPlus size={14} />}
                className={css({
                  color: "#6B7280",
                  fontSize: "13px",
                  "&:hover": {
                    backgroundColor: "#F3F4F6",
                  },
                })}
              >
                Add Member
              </Button>
            </Group>

            <Button
              className={css({
                backgroundColor: "#3B82F6",
                "&:hover": {
                  backgroundColor: "#2563EB",
                },
              })}
              leftSection={<HiPlus size={14} />}
              size="sm"
            >
              New Board
            </Button>
          </Group>
        </Group>

        {/* Tabs */}
        <Tabs defaultValue="board" variant="pills">
          <Tabs.List
            className={css({
              backgroundColor: "transparent",
              gap: "4px",
            })}
          >
            <Tabs.Tab 
              value="overview"
              className={css({
                fontSize: "14px",
                fontWeight: "500",
                color: "#6B7280",
                "&[data-active]": {
                  backgroundColor: "#F3F4F6",
                  color: "#1F2937",
                },
              })}
            >
              📊 Overview
            </Tabs.Tab>
            
            <Tabs.Tab 
              value="list"
              className={css({
                fontSize: "14px",
                fontWeight: "500",
                color: "#6B7280",
                "&[data-active]": {
                  backgroundColor: "#F3F4F6",
                  color: "#1F2937",
                },
              })}
            >
              📋 List
            </Tabs.Tab>
            
            <Tabs.Tab 
              value="board"
              className={css({
                fontSize: "14px",
                fontWeight: "500",
                color: "#6B7280",
                "&[data-active]": {
                  backgroundColor: "#3B82F6",
                  color: "white",
                },
              })}
            >
              📋 Board
            </Tabs.Tab>
            
            <Tabs.Tab 
              value="calendar"
              className={css({
                fontSize: "14px",
                fontWeight: "500",
                color: "#6B7280",
                "&[data-active]": {
                  backgroundColor: "#F3F4F6",
                  color: "#1F2937",
                },
              })}
            >
              📅 Calendar
            </Tabs.Tab>
            
            <Button
              variant="subtle"
              size="xs"
              leftSection={<HiPlus size={12} />}
              className={css({
                color: "#6B7280",
                fontSize: "13px",
                marginLeft: "8px",
                "&:hover": {
                  backgroundColor: "#F3F4F6",
                },
              })}
            >
              Add View
            </Button>
          </Tabs.List>
        </Tabs>
      </Box>
    </Box>
  );
}
