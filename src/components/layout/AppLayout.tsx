import { Box } from "@mantine/core";
import { css } from "styled-system/css";
import { Sidebar } from "./Sidebar";
import { useState } from "react";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <Box
      className={css({
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#FAFBFC",
      })}
    >
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content */}
      <Box
        className={css({
          flex: 1,
          display: "flex",
          flexDirection: "column",
        })}
      >
        {children}
      </Box>
    </Box>
  );
}
