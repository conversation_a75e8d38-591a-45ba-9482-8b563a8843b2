import { Box, Text, Stack, Group, Avatar, UnstyledButton, Badge } from "@mantine/core";
import { Link, useLocation } from "@tanstack/react-router";
import { 
  HiHome, 
  HiClipboardList, 
  HiFolder, 
  HiUsers, 
  HiInbox,
  HiSupport,
  HiCog,
  HiChevronLeft,
  HiChevronRight,
  HiDatabase,
  HiGlobe,
  HiClock,
  HiBell,
  HiUserGroup
} from "react-icons/hi";
import { css } from "styled-system/css";
import { useState } from "react";

interface SidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

const navigationItems = [
  { icon: HiHome, label: "Dashboard", path: "/" },
  { icon: HiGlobe, label: "Appearance", path: "/appearance" },
  { icon: HiDatabase, label: "Database", path: "/database" },
  { icon: HiFolder, label: "Connections", path: "/connections" },
  { icon: HiClock, label: "Timezones", path: "/timezones" },
  { icon: Hi<PERSON><PERSON>, label: "Notifications", path: "/notifications", badge: 4 },
];

const sisyphusVenturesItems = [
  { icon: HiUserGroup, label: "User management", path: "/user-management" },
  { icon: HiCog, label: "Security & access", path: "/security" },
  { icon: HiSupport, label: "Authentication", path: "/authentication" },
  { icon: HiClipboardList, label: "Payments", path: "/payments" },
  { icon: HiInbox, label: "Import data", path: "/import-data" },
  { icon: HiFolder, label: "Export data", path: "/export-data" },
];

const bottomItems = [
  { icon: HiCog, label: "Settings", path: "/settings" },
  { icon: HiSupport, label: "Documentation", path: "/docs" },
  { icon: HiGlobe, label: "Open in browser", path: "/browser", external: true },
];

export function Sidebar({ collapsed = false, onToggle }: SidebarProps) {
  const location = useLocation();

  const NavItem = ({ icon: Icon, label, path, active = false, badge, external = false }: any) => (
    <UnstyledButton
      component={external ? "a" : Link}
      to={external ? undefined : path}
      href={external ? path : undefined}
      target={external ? "_blank" : undefined}
      className={css({
        display: "flex",
        alignItems: "center",
        gap: "12px",
        padding: "8px 12px",
        borderRadius: "8px",
        width: "100%",
        color: active ? "#3B82F6" : "#6B7280",
        backgroundColor: active ? "#EFF6FF" : "transparent",
        fontSize: "14px",
        fontWeight: active ? "600" : "500",
        transition: "all 0.2s ease",
        "&:hover": {
          backgroundColor: active ? "#EFF6FF" : "#F9FAFB",
          color: active ? "#3B82F6" : "#374151",
        },
      })}
    >
      <Icon size={18} />
      {!collapsed && (
        <>
          <Text size="sm" style={{ flex: 1 }}>{label}</Text>
          {badge && (
            <Badge size="xs" color="blue" variant="filled">
              {badge}
            </Badge>
          )}
          {external && <Text size="xs">↗</Text>}
        </>
      )}
    </UnstyledButton>
  );

  return (
    <Box
      className={css({
        width: collapsed ? "60px" : "280px",
        height: "100vh",
        backgroundColor: "white",
        borderRight: "1px solid #E5E7EB",
        display: "flex",
        flexDirection: "column",
        transition: "width 0.3s ease",
        flexShrink: 0,
      })}
    >
      {/* Header */}
      <Box
        className={css({
          padding: "16px",
          borderBottom: "1px solid #E5E7EB",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        })}
      >
        {!collapsed && (
          <Group gap="xs">
            <Box
              className={css({
                width: "24px",
                height: "24px",
                backgroundColor: "#1F2937",
                borderRadius: "6px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              })}
            >
              <Text size="xs" fw={700} c="white">U</Text>
            </Box>
            <Text fw={600} size="sm" c="#1F2937">Untitled UI</Text>
            <Badge size="xs" variant="light" color="gray">v3.0</Badge>
          </Group>
        )}
        
        <UnstyledButton
          onClick={onToggle}
          className={css({
            padding: "4px",
            borderRadius: "4px",
            color: "#6B7280",
            "&:hover": {
              backgroundColor: "#F3F4F6",
            },
          })}
        >
          {collapsed ? <HiChevronRight size={16} /> : <HiChevronLeft size={16} />}
        </UnstyledButton>
      </Box>

      {/* Navigation */}
      <Box className={css({ flex: 1, padding: "16px 12px", overflow: "hidden" })}>
        {/* General Section */}
        {!collapsed && (
          <Text size="xs" fw={600} c="#6B7280" tt="uppercase" mb="sm">
            GENERAL
          </Text>
        )}
        <Stack gap="xs" mb="xl">
          {navigationItems.map((item) => (
            <NavItem
              key={item.path}
              {...item}
              active={location.pathname === item.path}
            />
          ))}
        </Stack>

        {/* Sisyphus Ventures Section */}
        {!collapsed && (
          <Text size="xs" fw={600} c="#6B7280" tt="uppercase" mb="sm">
            SISYPHUS VENTURES
          </Text>
        )}
        <Stack gap="xs">
          {sisyphusVenturesItems.map((item) => (
            <NavItem
              key={item.path}
              {...item}
              active={location.pathname === item.path}
            />
          ))}
        </Stack>
      </Box>

      {/* Bottom Section */}
      <Box className={css({ padding: "12px", borderTop: "1px solid #E5E7EB" })}>
        <Stack gap="xs">
          {bottomItems.map((item) => (
            <NavItem
              key={item.path}
              {...item}
              active={location.pathname === item.path}
            />
          ))}
        </Stack>
      </Box>
    </Box>
  );
}
