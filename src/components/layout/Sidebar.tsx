import { Box, Text, Stack, Group, Avatar, UnstyledButton, Badge } from "@mantine/core";
import { Link, useLocation } from "@tanstack/react-router";
import { 
  HiHome, 
  HiClipboardList, 
  HiFolder, 
  HiUsers, 
  HiInbox,
  HiSupport,
  HiCog,
  HiChevronLeft,
  HiChevronRight
} from "react-icons/hi";
import { css } from "styled-system/css";
import { useState } from "react";

interface SidebarProps {
  collapsed?: boolean;
  onToggle?: () => void;
}

const navigationItems = [
  { icon: HiHome, label: "Home", path: "/" },
  { icon: HiClipboardList, label: "My Task", path: "/tasks" },
  { icon: HiFolder, label: "Projects", path: "/projects" },
  { icon: HiUsers, label: "Team", path: "/team" },
  { icon: HiInbox, label: "Inbox", path: "/inbox" },
];

const projects = [
  { name: "Unifine Design System", color: "#3B82F6", active: true },
  { name: "Executive Leadership", color: "#10B981" },
  { name: "Homepage Update", color: "#F59E0B" },
  { name: "Unifine Mobile App", color: "#8B5CF6" },
  { name: "Unifine Mobile App", color: "#EF4444" },
];

const otherItems = [
  { icon: HiSupport, label: "Support", path: "/support" },
  { icon: HiCog, label: "Settings", path: "/settings" },
];

export function Sidebar({ collapsed = false, onToggle }: SidebarProps) {
  const location = useLocation();

  const NavItem = ({ icon: Icon, label, path, active = false }: any) => (
    <UnstyledButton
      component={Link}
      to={path}
      className={css({
        display: "flex",
        alignItems: "center",
        gap: "12px",
        padding: "8px 12px",
        borderRadius: "8px",
        width: "100%",
        color: active ? "#3B82F6" : "#6B7280",
        backgroundColor: active ? "#EFF6FF" : "transparent",
        fontSize: "14px",
        fontWeight: active ? "600" : "500",
        transition: "all 0.2s ease",
        "&:hover": {
          backgroundColor: active ? "#EFF6FF" : "#F9FAFB",
          color: active ? "#3B82F6" : "#374151",
        },
      })}
    >
      <Icon size={18} />
      {!collapsed && <Text size="sm">{label}</Text>}
    </UnstyledButton>
  );

  const ProjectItem = ({ name, color, active = false }: any) => (
    <UnstyledButton
      component={Link}
      to="/boards"
      className={css({
        display: "flex",
        alignItems: "center",
        gap: "8px",
        padding: "6px 12px",
        borderRadius: "6px",
        width: "100%",
        fontSize: "13px",
        color: active ? "#1F2937" : "#6B7280",
        backgroundColor: active ? "#F3F4F6" : "transparent",
        fontWeight: active ? "500" : "400",
        transition: "all 0.2s ease",
        "&:hover": {
          backgroundColor: "#F9FAFB",
          color: "#374151",
        },
      })}
    >
      <Box
        className={css({
          width: "8px",
          height: "8px",
          borderRadius: "50%",
          backgroundColor: color,
          flexShrink: 0,
        })}
      />
      {!collapsed && (
        <Text size="xs" className={css({ overflow: "hidden", textOverflow: "ellipsis" })}>
          {name}
        </Text>
      )}
    </UnstyledButton>
  );

  return (
    <Box
      className={css({
        width: collapsed ? "60px" : "240px",
        height: "100vh",
        backgroundColor: "#FAFBFC",
        borderRight: "1px solid #E5E7EB",
        display: "flex",
        flexDirection: "column",
        transition: "width 0.3s ease",
        position: "relative",
      })}
    >
      {/* Header */}
      <Box
        className={css({
          padding: "16px",
          borderBottom: "1px solid #E5E7EB",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        })}
      >
        {!collapsed && (
          <Group gap="xs">
            <Box
              className={css({
                width: "24px",
                height: "24px",
                backgroundColor: "#3B82F6",
                borderRadius: "6px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              })}
            >
              <Text size="xs" fw={700} c="white">N</Text>
            </Box>
            <Text fw={600} size="sm" c="#1F2937">NovaFlow</Text>
          </Group>
        )}
        
        <UnstyledButton
          onClick={onToggle}
          className={css({
            padding: "4px",
            borderRadius: "4px",
            color: "#6B7280",
            "&:hover": {
              backgroundColor: "#F3F4F6",
            },
          })}
        >
          {collapsed ? <HiChevronRight size={16} /> : <HiChevronLeft size={16} />}
        </UnstyledButton>
      </Box>

      {/* Navigation */}
      <Box className={css({ flex: 1, padding: "16px 12px", overflow: "hidden" })}>
        <Stack gap="xs">
          {navigationItems.map((item) => (
            <NavItem
              key={item.path}
              {...item}
              active={location.pathname === item.path || (item.path === "/projects" && location.pathname === "/boards")}
            />
          ))}
        </Stack>

        {/* Projects Section */}
        {!collapsed && (
          <Box mt="xl">
            <Group justify="space-between" mb="sm">
              <Text size="xs" fw={600} c="#6B7280" tt="uppercase">
                PROJECT
              </Text>
              <UnstyledButton
                className={css({
                  color: "#6B7280",
                  "&:hover": { color: "#374151" },
                })}
              >
                <Text size="xs">+</Text>
              </UnstyledButton>
            </Group>
            <Stack gap="xs">
              {projects.map((project, index) => (
                <ProjectItem key={index} {...project} />
              ))}
            </Stack>
          </Box>
        )}

        {/* Others Section */}
        <Box mt="xl">
          {!collapsed && (
            <Text size="xs" fw={600} c="#6B7280" tt="uppercase" mb="sm">
              Others
            </Text>
          )}
          <Stack gap="xs">
            {otherItems.map((item) => (
              <NavItem
                key={item.path}
                {...item}
                active={location.pathname === item.path}
              />
            ))}
          </Stack>
        </Box>
      </Box>
    </Box>
  );
}
