import {
  Modal,
  TextInput,
  Select,
  Button,
  Group,
  Stack,
  ColorInput,
} from "@mantine/core";
import { useForm } from "react-hook-form";
import { useKanbanStore } from "@/stores/kanbanStore";
import { CardStatus } from "@/types/kanban";

interface AddColumnModalProps {
  opened: boolean;
  onClose: () => void;
}

interface ColumnForm {
  title: string;
  status: CardStatus;
  color: string;
}

const statusOptions = [
  { value: "todo", label: "To Do" },
  { value: "in-progress", label: "In Progress" },
  { value: "review", label: "Review" },
  { value: "done", label: "Done" },
];

const colorPresets = [
  "#e3f2fd", // Light Blue
  "#fff3e0", // Light Orange
  "#f3e5f5", // Light Purple
  "#e8f5e8", // Light Green
  "#fce4ec", // Light Pink
  "#f1f8e9", // Light Lime
  "#fff8e1", // Light Yellow
  "#e0f2f1", // Light Teal
];

export function AddColumnModal({ opened, onClose }: AddColumnModalProps) {
  const { addColumn } = useKanbanStore();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ColumnForm>({
    defaultValues: {
      title: "",
      status: "todo",
      color: "#e3f2fd",
    },
  });

  const watchedColor = watch("color");

  const onSubmit = (data: ColumnForm) => {
    addColumn({
      title: data.title,
      status: data.status,
      color: data.color,
    });

    reset();
    onClose();
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Add New Column"
      size="md"
      centered
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Column Title"
            placeholder="Enter column title"
            required
            error={errors.title?.message}
            {...register("title", {
              required: "Title is required",
              minLength: {
                value: 1,
                message: "Title must not be empty",
              },
            })}
          />

          <Select
            label="Status"
            data={statusOptions}
            defaultValue="todo"
            {...register("status")}
            onChange={(value) => setValue("status", value as CardStatus)}
          />

          <ColorInput
            label="Column Color"
            placeholder="Pick a color"
            value={watchedColor}
            onChange={(value) => setValue("color", value)}
            swatches={colorPresets}
            swatchesPerRow={4}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit">
              Add Column
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}
