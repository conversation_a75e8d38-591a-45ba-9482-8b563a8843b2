import {
  Modal,
  TextInput,
  Textarea,
  Select,
  Button,
  Group,
  Stack,
  TagsInput,
} from "@mantine/core";
import { useForm } from "react-hook-form";
import { useKanbanStore } from "@/stores/kanbanStore";
import { Priority } from "@/types/kanban";

interface AddCardModalProps {
  opened: boolean;
  onClose: () => void;
  columnId: string;
}

interface CardForm {
  title: string;
  description: string;
  priority: Priority;
  assignee: string;
  tags: string[];
  dueDate: string;
}

export function AddCardModal({ opened, onClose, columnId }: AddCardModalProps) {
  const { addCard } = useKanbanStore();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CardForm>({
    defaultValues: {
      title: "",
      description: "",
      priority: "medium",
      assignee: "",
      tags: [],
      dueDate: "",
    },
  });

  const watchedTags = watch("tags");

  const onSubmit = (data: CardForm) => {
    addCard(columnId, {
      title: data.title,
      description: data.description || undefined,
      priority: data.priority,
      assignee: data.assignee || undefined,
      tags: data.tags.length > 0 ? data.tags : undefined,
      dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
    });

    reset();
    onClose();
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Add New Card"
      size="md"
      centered
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack gap="md">
          <TextInput
            label="Title"
            placeholder="Enter card title"
            required
            error={errors.title?.message}
            {...register("title", {
              required: "Title is required",
              minLength: {
                value: 1,
                message: "Title must not be empty",
              },
            })}
          />

          <Textarea
            label="Description"
            placeholder="Enter card description (optional)"
            rows={3}
            {...register("description")}
          />

          <Select
            label="Priority"
            data={[
              { value: "low", label: "Low" },
              { value: "medium", label: "Medium" },
              { value: "high", label: "High" },
              { value: "urgent", label: "Urgent" },
            ]}
            defaultValue="medium"
            {...register("priority")}
            onChange={(value) => setValue("priority", value as Priority)}
          />

          <TextInput
            label="Assignee"
            placeholder="Enter assignee name (optional)"
            {...register("assignee")}
          />

          <TagsInput
            label="Tags"
            placeholder="Add tags (optional)"
            value={watchedTags}
            onChange={(value) => setValue("tags", value)}
          />

          <TextInput
            label="Due Date"
            type="date"
            {...register("dueDate")}
          />

          <Group justify="flex-end" mt="md">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit">
              Add Card
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
}
