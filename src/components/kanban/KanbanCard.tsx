import { Box, Text, Badge, Group, Avatar, ActionIcon, Menu } from "@mantine/core";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { forwardRef, memo, useMemo, useCallback } from "react";
import {
  HiDotsVertical,
  HiClock,
  Hi<PERSON>ser,
  HiChat,
  HiPaperClip,
  HiEye,
  HiHeart,
  HiCheckCircle
} from "react-icons/hi";
import { css } from "styled-system/css";
import { KanbanCard as KanbanCardType } from "@/types/kanban";
import { useKanbanStore } from "@/stores/kanbanStore";

interface KanbanCardProps {
  card: KanbanCardType;
  isDragging?: boolean;
}

// Memoized card content to prevent re-renders during drag
const CardContent = memo(({
  card,
  getTagColor,
  formatDate,
  handleEditCard,
  handleDeleteCard,
  isDragging
}: {
  card: KanbanCardType;
  getTagColor: (tag: string) => string;
  formatDate: (date: Date) => string;
  handleEditCard: () => void;
  handleDeleteCard: () => void;
  isDragging: boolean;
}) => {
  const isOverdue = useMemo(() =>
    card.dueDate && new Date(card.dueDate) < new Date(),
    [card.dueDate]
  );

  return (
    <>
      {/* Tags */}
      <Group gap="6px" mb="12px" wrap="wrap">
        {card.tags.slice(0, 2).map((tag) => (
          <Badge
            key={tag}
            size="xs"
            variant="light"
            className={css({
              backgroundColor: `${getTagColor(tag)}15`,
              color: getTagColor(tag),
              fontSize: "11px",
              fontWeight: "500",
              padding: "4px 8px",
              borderRadius: "4px",
              border: "none",
            })}
          >
            {tag}
          </Badge>
        ))}

        {!isDragging && (
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                color="gray"
                size="xs"
                onClick={(e) => e.stopPropagation()}
                className={css({
                  marginLeft: "auto",
                  opacity: 0.6,
                  "&:hover": { opacity: 1 },
                })}
              >
                <HiDotsVertical size={12} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item onClick={handleEditCard}>
                Edit Card
              </Menu.Item>
              <Menu.Item color="red" onClick={handleDeleteCard}>
                Delete Card
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        )}
      </Group>

      {/* Card Title */}
      <Text
        fw={600}
        size="sm"
        mb="8px"
        c="#1F2937"
        className={css({
          lineHeight: "1.4",
          wordBreak: "break-word",
          fontSize: "14px",
        })}
      >
        {card.title}
      </Text>

      {/* Card Description */}
      {card.description && (
        <Text
          size="xs"
          c="#6B7280"
          mb="12px"
          className={css({
            lineHeight: "1.4",
            wordBreak: "break-word",
            fontSize: "12px",
          })}
        >
          {card.description}
        </Text>
      )}

      {/* Due Date */}
      {card.dueDate && (
        <Group gap="4px" mb="12px">
          <HiClock size={12} color="#6B7280" />
          <Text
            size="xs"
            c={isOverdue ? "#EF4444" : "#6B7280"}
            fw={isOverdue ? 600 : 400}
            className={css({ fontSize: "11px" })}
          >
            {formatDate(new Date(card.dueDate))}
          </Text>
          {isOverdue && (
            <Badge
              size="xs"
              color="red"
              variant="light"
              className={css({
                fontSize: "10px",
                padding: "2px 6px",
                marginLeft: "4px",
              })}
            >
              Urgent
            </Badge>
          )}
        </Group>
      )}

      {/* Card Footer */}
      <Group justify="space-between" align="center" mt="auto">
        {/* Left side - Metrics */}
        <Group gap="12px">
          <Group gap="4px">
            <HiChat size={12} color="#6B7280" />
            <Text size="xs" c="#6B7280" className={css({ fontSize: "11px" })}>
              {Math.floor(Math.random() * 5) + 1}
            </Text>
          </Group>

          <Group gap="4px">
            <HiPaperClip size={12} color="#6B7280" />
            <Text size="xs" c="#6B7280" className={css({ fontSize: "11px" })}>
              {Math.floor(Math.random() * 3) + 1}
            </Text>
          </Group>

          <Group gap="4px">
            <HiCheckCircle size={12} color="#6B7280" />
            <Text size="xs" c="#6B7280" className={css({ fontSize: "11px" })}>
              {Math.floor(Math.random() * 8) + 1}
            </Text>
          </Group>
        </Group>

        {/* Right side - Assignees */}
        <Avatar.Group spacing="xs">
          <Avatar size="sm" color="blue" className={css({ border: "2px solid white" })}>
            <Text size="xs">JD</Text>
          </Avatar>
          <Avatar size="sm" color="green" className={css({ border: "2px solid white" })}>
            <Text size="xs">SM</Text>
          </Avatar>
          {Math.random() > 0.5 && (
            <Avatar size="sm" color="orange" className={css({ border: "2px solid white" })}>
              <Text size="xs">MJ</Text>
            </Avatar>
          )}
        </Avatar.Group>
      </Group>
    </>
  );
});

CardContent.displayName = "CardContent";

export const KanbanCard = memo(forwardRef<HTMLDivElement, KanbanCardProps>(
  ({ card, isDragging = false }, ref) => {
    const { deleteCard, updateCard } = useKanbanStore();

    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging: isSortableDragging,
    } = useSortable({
      id: card.id,
      data: {
        type: "card",
        card,
      },
    });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    // Memoize callbacks to prevent re-renders
    const handleDeleteCard = useCallback(() => {
      // Find which column contains this card
      const { board } = useKanbanStore.getState();
      if (!board) return;

      const column = board.columns.find(col =>
        col.cards.some(c => c.id === card.id)
      );

      if (column) {
        deleteCard(card.id, column.id);
      }
    }, [deleteCard, card.id]);

    const handleEditCard = useCallback(() => {
      const newTitle = prompt("Enter new card title:", card.title);
      if (newTitle && newTitle.trim()) {
        updateCard(card.id, { title: newTitle.trim() });
      }
    }, [updateCard, card.id, card.title]);

    // Memoize expensive functions
    const getTagColor = useCallback((tag: string) => {
      const colors: Record<string, string> = {
        "UI Design": "#3B82F6",
        "Branding": "#10B981",
        "Backend": "#8B5CF6",
        "Server": "#F59E0B",
        "Testing": "#EF4444",
        "Interaction": "#6366F1",
        "UX Design": "#06B6D4",
        "Research": "#10B981",
        "UI Development": "#8B5CF6",
        "Frontend": "#F59E0B",
      };
      return colors[tag] || "#6B7280";
    }, []);

    const formatDate = useCallback((date: Date) => {
      return new Intl.DateTimeFormat("en-US", {
        month: "short",
        day: "numeric",
      }).format(date);
    }, []);

    return (
      <Box
        ref={(node) => {
          setNodeRef(node);
          if (ref && typeof ref === "function") {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
        }}
        style={style}
        className={css({
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "16px",
          border: "1px solid #E5E7EB",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.05)",
          cursor: "grab",
          opacity: isDragging || isSortableDragging ? 0.5 : 1,
          transform: isDragging || isSortableDragging ? "scale(1.05)" : undefined,
          transition: isDragging || isSortableDragging ? "none" : "all 0.2s ease",
          marginBottom: "12px",
          "&:hover": {
            boxShadow: isDragging || isSortableDragging ? undefined : "0 4px 12px rgba(0, 0, 0, 0.1)",
            borderColor: isDragging || isSortableDragging ? undefined : "#D1D5DB",
          },
          "&:active": {
            cursor: "grabbing",
          },
        })}
        {...attributes}
        {...listeners}
      >
        <CardContent
          card={card}
          getTagColor={getTagColor}
          formatDate={formatDate}
          handleEditCard={handleEditCard}
          handleDeleteCard={handleDeleteCard}
          isDragging={isDragging || isSortableDragging}
        />
      </Box>
    );
  }
));

KanbanCard.displayName = "KanbanCard";
