import { Box, Title, Text, Badge, ActionIcon, Menu, Button, Group } from "@mantine/core";
import { useSortable, SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { useDroppable } from "@dnd-kit/core";
import { useState, memo, useMemo, useCallback } from "react";
import { HiDotsVertical, HiPlus } from "react-icons/hi";
import { css } from "styled-system/css";
import { KanbanColumn as KanbanColumnType } from "@/types/kanban";
import { useKanbanStore } from "@/stores/kanbanStore";
import { KanbanCard } from "./KanbanCard";
import { AddCardModal } from "./AddCardModal";

interface KanbanColumnProps {
  column: KanbanColumnType;
  dropPlaceholder?: number | null;
  activeCardId?: string | null;
}

export const KanbanColumn = memo(function KanbanColumn({ column, dropPlaceholder, activeCardId }: KanbanColumnProps) {
  const { deleteColumn, updateColumn } = useKanbanStore();
  const [isAddCardModalOpen, setIsAddCardModalOpen] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef: setSortableRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: column.id,
    data: {
      type: "column",
      column,
    },
  });

  const { setNodeRef: setDroppableRef, isOver } = useDroppable({
    id: column.id,
    data: {
      type: "column",
      accepts: ["card"],
      column,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // Memoize card IDs to prevent re-renders
  const cardIds = useMemo(() => column.cards.map(card => card.id), [column.cards]);

  // Memoize callbacks to prevent re-renders
  const handleDeleteColumn = useCallback(() => {
    deleteColumn(column.id);
  }, [deleteColumn, column.id]);

  const handleEditColumn = useCallback(() => {
    const newTitle = prompt("Enter new column title:", column.title);
    if (newTitle && newTitle.trim()) {
      updateColumn(column.id, { title: newTitle.trim() });
    }
  }, [updateColumn, column.id, column.title]);

  // Placeholder card component (Trello-style)
  const PlaceholderCard = () => (
    <Box
      className={css({
        backgroundColor: "#E5E7EB",
        borderRadius: "12px",
        padding: "16px",
        border: "2px dashed #9CA3AF",
        marginBottom: "12px",
        minHeight: "80px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        opacity: 0.7,
        animation: "pulse 1.5s ease-in-out infinite",
        "@keyframes pulse": {
          "0%, 100%": { opacity: 0.7 },
          "50%": { opacity: 0.5 },
        },
      })}
    >
      <Text
        className={css({
          color: "#6B7280",
          fontSize: "14px",
          fontStyle: "italic",
        })}
      >
        Drop card here
      </Text>
    </Box>
  );

  return (
    <>
      <Box
        ref={(node) => {
          setSortableRef(node);
          setDroppableRef(node);
        }}
        style={style}
        className={css({
          width: "320px",
          minWidth: "320px",
          "@media (max-width: 768px)": {
            width: "280px",
            minWidth: "280px",
          },
          backgroundColor: "#F9FAFB",
          borderRadius: "8px",
          padding: "16px",
          boxShadow: "none",
          border: "1px solid #E5E7EB",
          opacity: isDragging ? 0.7 : 1,
          display: "flex",
          flexDirection: "column",
          maxHeight: "calc(100vh - 200px)",
          transition: "all 0.2s ease",
          "&:hover": {
            backgroundColor: "#F3F4F6",
          },
        })}
        {...attributes}
      >
        {/* Column Header */}
        <Box
          className={css({
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            marginBottom: "16px",
            paddingBottom: "0px",
            borderBottom: "none",
          })}
          {...listeners}
        >
          <Box className={css({ display: "flex", alignItems: "center", gap: "8px" })}>
            <Text
              fw={600}
              size="sm"
              c="#1F2937"
              className={css({ fontSize: "14px" })}
            >
              {column.title}
            </Text>
            <Badge
              size="sm"
              variant="light"
              className={css({
                backgroundColor: "#F3F4F6",
                color: "#6B7280",
                fontSize: "12px",
                fontWeight: "500",
                padding: "4px 8px",
                borderRadius: "12px",
                border: "none",
              })}
            >
              {column.cards.length}
            </Badge>
          </Box>

          <Group gap="4px">
            <ActionIcon
              variant="subtle"
              color="gray"
              size="sm"
              onClick={() => setIsAddCardModalOpen(true)}
              className={css({
                color: "#6B7280",
                "&:hover": {
                  backgroundColor: "#F3F4F6",
                },
              })}
            >
              <HiPlus size={14} />
            </ActionIcon>

            <Menu shadow="md" width={200}>
              <Menu.Target>
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="sm"
                  className={css({
                    color: "#6B7280",
                    "&:hover": {
                      backgroundColor: "#F3F4F6",
                    },
                  })}
                >
                  <HiDotsVertical size={14} />
                </ActionIcon>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Item onClick={handleEditColumn}>
                  Edit Column
                </Menu.Item>
                <Menu.Item color="red" onClick={handleDeleteColumn}>
                  Delete Column
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
        </Box>

        {/* Cards Container */}
        <Box
          className={css({
            flex: 1,
            overflowY: "auto",
            display: "flex",
            flexDirection: "column",
            gap: "0.75rem",
            paddingRight: "0.25rem",
          })}
        >
          <SortableContext items={cardIds} strategy={verticalListSortingStrategy}>
            {column.cards.map((card, index) => (
              <Box key={card.id}>
                {/* Show placeholder before this card if needed */}
                {dropPlaceholder === index && <PlaceholderCard />}
                {/* Hide the active card being dragged */}
                {card.id !== activeCardId && <KanbanCard card={card} />}
              </Box>
            ))}
            {/* Show placeholder at the end if needed */}
            {dropPlaceholder === column.cards.length && <PlaceholderCard />}
          </SortableContext>
        </Box>

        {/* Add Task Button */}
        <Button
          variant="subtle"
          leftSection={<HiPlus size={14} />}
          onClick={() => setIsAddCardModalOpen(true)}
          className={css({
            marginTop: "12px",
            color: "#6B7280",
            fontSize: "13px",
            fontWeight: "500",
            justifyContent: "flex-start",
            backgroundColor: "transparent",
            "&:hover": {
              backgroundColor: "#F3F4F6",
              color: "#374151",
            },
          })}
        >
          + Add Task
        </Button>
      </Box>

      <AddCardModal
        opened={isAddCardModalOpen}
        onClose={() => setIsAddCardModalOpen(false)}
        columnId={column.id}
      />
    </>
  );
});
