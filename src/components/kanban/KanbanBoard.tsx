import { Box, Group, Button, ActionIcon } from "@mantine/core";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragMoveEvent,
} from "@dnd-kit/core";
import { SortableContext, horizontalListSortingStrategy } from "@dnd-kit/sortable";
import { useState, useCallback, useMemo, useRef } from "react";
import { HiPlus } from "react-icons/hi";
import { css } from "styled-system/css";
import { KanbanBoard as KanbanBoardType } from "@/types/kanban";
import { useKanbanStore } from "@/stores/kanbanStore";
import { useThrottle } from "@/hooks/useThrottle";
import { KanbanColumn } from "./KanbanColumn";
import { KanbanCard } from "./KanbanCard";
import { AddColumnModal } from "./AddColumnModal";

interface KanbanBoardProps {
  board: KanbanBoardType;
}

export function KanbanBoard({ board }: KanbanBoardProps) {
  const { moveCard, reorderColumns, reorderCards } = useKanbanStore();
  const [activeId, setActiveId] = useState<string | null>(null);
  const [activeCard, setActiveCard] = useState<any>(null);
  const [isAddColumnModalOpen, setIsAddColumnModalOpen] = useState(false);

  // Track drop placeholder position
  const [dropPlaceholder, setDropPlaceholder] = useState<{
    columnId: string;
    index: number;
  } | null>(null);

  // Track the last container to prevent excessive updates
  const lastOverContainer = useRef<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 10, // Increased distance to prevent accidental drags
      },
    })
  );

  // Memoize the findContainer function
  const findContainer = useCallback((id: string) => {
    // Check if it's a column
    if (board.columns.find(col => col.id === id)) {
      return id;
    }

    // Find which column contains this card
    return board.columns.find(col =>
      col.cards.some(card => card.id === id)
    )?.id;
  }, [board.columns]);

  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    setDropPlaceholder(null);
    lastOverContainer.current = null;

    // Find the active card for the overlay
    for (const column of board.columns) {
      const card = column.cards.find((c) => c.id === active.id);
      if (card) {
        setActiveCard(card);
        break;
      }
    }
  }, [board.columns]);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    const { active, over } = event;
    if (!over) {
      setDropPlaceholder(null);
      return;
    }

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the containers
    const activeContainer = findContainer(activeId);
    const overContainer = findContainer(overId);

    if (!activeContainer || !overContainer) {
      setDropPlaceholder(null);
      return;
    }

    // Get the target column
    const overColumn = board.columns.find(col => col.id === overContainer);
    if (!overColumn) {
      setDropPlaceholder(null);
      return;
    }

    let dropIndex = overColumn.cards.length; // Default to end

    // If hovering over a specific card, calculate position
    if (overId !== overContainer) {
      const overCardIndex = overColumn.cards.findIndex(card => card.id === overId);
      if (overCardIndex !== -1) {
        dropIndex = overCardIndex;
      }
    }

    // For same column, adjust index if dragging down
    if (activeContainer === overContainer) {
      const activeIndex = overColumn.cards.findIndex(card => card.id === activeId);
      if (activeIndex !== -1 && activeIndex < dropIndex) {
        dropIndex = dropIndex - 1;
      }

      // Don't show placeholder if dropping on the same position
      if (activeIndex === dropIndex) {
        setDropPlaceholder(null);
        return;
      }
    }

    // Set the drop placeholder
    setDropPlaceholder({
      columnId: overContainer,
      index: dropIndex,
    });

    // Actually move the card for cross-column moves
    if (activeContainer !== overContainer) {
      // Only update if the container actually changed
      if (lastOverContainer.current !== overContainer) {
        lastOverContainer.current = overContainer;
        moveCard(activeId, activeContainer, overContainer, dropIndex);
      }
    }
  }, [findContainer, board.columns, moveCard]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);
    setActiveCard(null);

    // Use the placeholder position for final placement
    const finalDropPosition = dropPlaceholder;
    setDropPlaceholder(null);
    lastOverContainer.current = null;

    if (!over && !finalDropPosition) return;

    const activeId = active.id as string;
    const overId = over?.id as string;

    // Handle column reordering
    if (overId && board.columns.find(col => col.id === activeId) && board.columns.find(col => col.id === overId)) {
      const activeIndex = board.columns.findIndex(col => col.id === activeId);
      const overIndex = board.columns.findIndex(col => col.id === overId);

      if (activeIndex !== overIndex) {
        reorderColumns(activeIndex, overIndex);
      }
      return;
    }

    // Handle card positioning using the placeholder position
    if (finalDropPosition) {
      const activeContainer = findContainer(activeId);
      if (!activeContainer) return;

      const targetColumnId = finalDropPosition.columnId;
      const targetIndex = finalDropPosition.index;

      // If same column, reorder within column
      if (activeContainer === targetColumnId) {
        const column = board.columns.find(col => col.id === activeContainer);
        if (!column) return;

        const activeIndex = column.cards.findIndex(card => card.id === activeId);
        if (activeIndex !== targetIndex) {
          reorderCards(activeContainer, activeIndex, targetIndex);
        }
      } else {
        // Cross-column moves are already handled in dragOver
        // But we need to ensure final positioning is correct
        const targetColumn = board.columns.find(col => col.id === targetColumnId);
        if (targetColumn) {
          const currentIndex = targetColumn.cards.findIndex(card => card.id === activeId);
          if (currentIndex !== -1 && currentIndex !== targetIndex) {
            // Adjust position if needed
            reorderCards(targetColumnId, currentIndex, targetIndex);
          }
        }
      }
    }
  }, [board.columns, reorderColumns, reorderCards, moveCard, findContainer]);

  // Memoize column IDs to prevent re-renders
  const columnIds = useMemo(() => board.columns.map(col => col.id), [board.columns]);

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <Box
          className={css({
            display: "flex",
            gap: "20px",
            overflowX: "auto",
            paddingBottom: "16px",
            minHeight: "calc(100vh - 300px)",
            paddingRight: "16px",
            "@media (max-width: 768px)": {
              gap: "16px",
              paddingRight: "8px",
            },
          })}
        >
          <SortableContext items={columnIds} strategy={horizontalListSortingStrategy}>
            {board.columns.map((column) => (
              <KanbanColumn
                key={column.id}
                column={column}
                dropPlaceholder={dropPlaceholder?.columnId === column.id ? dropPlaceholder.index : null}
                activeCardId={activeId}
              />
            ))}
          </SortableContext>

          {/* Add Column Button */}
          <Box
            className={css({
              minWidth: "280px",
              display: "flex",
              alignItems: "flex-start",
              paddingTop: "1rem",
            })}
          >
            <Button
              variant="outline"
              leftSection={<HiPlus size={16} />}
              onClick={() => setIsAddColumnModalOpen(true)}
              className={css({
                width: "100%",
                height: "60px",
                borderStyle: "dashed",
                borderWidth: "2px",
                borderColor: "gray.300",
                backgroundColor: "white",
                "&:hover": {
                  backgroundColor: "gray.50",
                  borderColor: "blue.400",
                },
              })}
            >
              Add Column
            </Button>
          </Box>
        </Box>

        <DragOverlay>
          {activeId && activeCard ? (
            <Box
              className={css({
                backgroundColor: "white",
                borderRadius: "12px",
                padding: "16px",
                border: "1px solid #E5E7EB",
                boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
                transform: "rotate(5deg)",
                opacity: 0.95,
                minWidth: "280px",
                maxWidth: "280px",
              })}
            >
              <Box
                className={css({
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#1F2937",
                  marginBottom: "8px",
                })}
              >
                {activeCard.title}
              </Box>
              {activeCard.description && (
                <Box
                  className={css({
                    fontSize: "12px",
                    color: "#6B7280",
                    lineHeight: "1.4",
                  })}
                >
                  {activeCard.description.length > 50
                    ? `${activeCard.description.substring(0, 50)}...`
                    : activeCard.description
                  }
                </Box>
              )}
            </Box>
          ) : null}
        </DragOverlay>
      </DndContext>

      <AddColumnModal
        opened={isAddColumnModalOpen}
        onClose={() => setIsAddColumnModalOpen(false)}
      />
    </>
  );
}
