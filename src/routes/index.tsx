import { createFileRoute } from "@tanstack/react-router";
import { Container, Center, Text, Box, Title } from "@mantine/core";
import { AppLayout } from "@/components/layout/AppLayout";
import { css } from "styled-system/css";

export const Route = createFileRoute("/")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <AppLayout>
      {/* Header */}
      <Box
        className={css({
          backgroundColor: "white",
          borderBottom: "1px solid #E5E7EB",
          padding: "20px 24px",
        })}
      >
        <Title order={2} size="h2" mb={8} c="#1F2937">Dashboard</Title>
        <Text size="sm" c="#6B7280">
          Welcome to your dashboard overview.
        </Text>
      </Box>

      {/* Content */}
      <Box
        className={css({
          flex: 1,
          padding: "24px",
        })}
      >
        <Container size="md">
          <Center>
            <Text size="xl">Welcome to PinkTool!</Text>
          </Center>
        </Container>
      </Box>
    </AppLayout>
  );
}
