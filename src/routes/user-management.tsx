import { createFileRoute } from "@tanstack/react-router";
import { 
  Box, 
  Text, 
  Group, 
  Avatar, 
  Button, 
  TextInput, 
  Stack,
  Badge,
  ActionIcon,
  Container,
  Title,
  UnstyledButton
} from "@mantine/core";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>ilter, 
  HiPlus,
  HiLink,
  Hi<PERSON>serAdd
} from "react-icons/hi";
import { css } from "styled-system/css";
import { AppLayout } from "@/components/layout/AppLayout";
import { useState } from "react";

export const Route = createFileRoute("/user-management")({
  component: UserManagementPage,
});

// Mock user data - expanded to show more users
const mockUsers = [
  { id: 1, name: "<PERSON>", email: "<EMAIL>", avatar: null, role: "Ad<PERSON>" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", avatar: null, role: "User" },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", avatar: null, role: "User" },
  { id: 4, name: "<PERSON>", email: "<EMAIL>", avatar: null, role: "Editor" },
  { id: 5, name: "<PERSON>", email: "<EMAIL>", avatar: null, role: "User" },
  { id: 6, name: "Emma <PERSON>", email: "<EMAIL>", avatar: null, role: "User" },
  { id: 7, name: "David <PERSON>", email: "<EMAIL>", avatar: null, role: "Editor" },
  { id: 8, name: "Lisa Garcia", email: "<EMAIL>", avatar: null, role: "User" },
];

function UserManagementPage() {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredUsers = mockUsers.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <AppLayout>
        {/* Header */}
        <Box
          className={css({
            backgroundColor: "white",
            borderBottom: "1px solid #E5E7EB",
            padding: "20px 24px",
          })}
        >
          {/* Breadcrumb */}
          <Group gap="xs" mb="lg">
            <Box
              className={css({
                width: "20px",
                height: "20px",
                backgroundColor: "#EC4899",
                borderRadius: "4px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              })}
            >
              <Text size="xs" fw={700} c="white">S</Text>
            </Box>
            <Text size="sm" c="#6B7280">Sisyphus Ventures</Text>
            <Text size="sm" c="#D1D5DB">/</Text>
            <Text size="sm" c="#1F2937" fw={500}>User management</Text>
          </Group>

          {/* Header Content */}
          <Group justify="space-between" align="flex-start">
            <Box>
              <Title order={2} size="h2" mb={8} c="#1F2937">User management</Title>
              <Text size="sm" c="#6B7280">
                Manage your team members and their account permissions here.
              </Text>
            </Box>

            <Group gap="xs">
              <Avatar size="sm" color="blue">
                <Text size="xs" fw={500}>HJ</Text>
              </Avatar>
              <Text size="sm" fw={500} c="#1F2937">Hasan Johns</Text>
            </Group>
          </Group>
        </Box>

        {/* Content */}
        <Box
          className={css({
            flex: 1,
            padding: "24px",
          })}
        >
          {/* Users Header */}
          <Group justify="space-between" mb="lg">
            <Group gap="xs">
              <Title order={3} size="h4">All users</Title>
              <Badge variant="light" color="gray" size="sm">
                {filteredUsers.length}
              </Badge>
            </Group>

            <Group gap="sm">
              <TextInput
                placeholder="Search"
                leftSection={<HiSearch size={16} />}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={css({
                  "& input": {
                    backgroundColor: "white",
                    border: "1px solid #E5E7EB",
                    borderRadius: "8px",
                    fontSize: "14px",
                    "&:focus": {
                      borderColor: "#3B82F6",
                    },
                  },
                })}
                w={240}
              />
              
              <Button
                variant="outline"
                leftSection={<HiFilter size={16} />}
                className={css({
                  borderColor: "#E5E7EB",
                  color: "#6B7280",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                })}
              >
                Filters
              </Button>

              <Button
                className={css({
                  backgroundColor: "#1F2937",
                  color: "white",
                  "&:hover": {
                    backgroundColor: "#111827",
                  },
                })}
                leftSection={<HiPlus size={16} />}
              >
                Add user
              </Button>
            </Group>
          </Group>

          {/* User List or Empty State */}
          {filteredUsers.length === 0 && searchQuery !== "" ? (
            <Box
              className={css({
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                minHeight: "200px",
                textAlign: "center",
              })}
            >
              <Text size="sm" c="#6B7280" mb="sm">
                No users found matching "{searchQuery}"
              </Text>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSearchQuery("")}
                className={css({
                  borderColor: "#E5E7EB",
                  color: "#6B7280",
                })}
              >
                Clear search
              </Button>
            </Box>
          ) : mockUsers.length === 0 ? (
            <EmptyState />
          ) : (
            <UserList users={filteredUsers} />
          )}
        </Box>
    </AppLayout>
  );
}

function EmptyState() {
  return (
    <Box
      className={css({
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "400px",
        textAlign: "center",
      })}
    >
      {/* User avatars illustration */}
      <Box
        className={css({
          position: "relative",
          marginBottom: "24px",
          width: "200px",
          height: "120px",
        })}
      >
        {/* Multiple overlapping user avatars to simulate the illustration */}
        <Avatar
          size="lg"
          color="gray"
          className={css({
            position: "absolute",
            top: "10px",
            left: "20px",
            opacity: 0.6,
          })}
        >
          <HiUserAdd size={24} />
        </Avatar>
        <Avatar
          size="lg"
          color="blue"
          className={css({
            position: "absolute",
            top: "30px",
            left: "60px",
            opacity: 0.8,
          })}
        >
          <Text size="sm">U</Text>
        </Avatar>
        <Avatar
          size="lg"
          color="green"
          className={css({
            position: "absolute",
            top: "20px",
            left: "100px",
            opacity: 0.7,
          })}
        >
          <Text size="sm">M</Text>
        </Avatar>
        <Avatar
          size="md"
          color="purple"
          className={css({
            position: "absolute",
            top: "60px",
            left: "40px",
            opacity: 0.5,
          })}
        >
          <Text size="xs">A</Text>
        </Avatar>
        <Avatar
          size="md"
          color="orange"
          className={css({
            position: "absolute",
            top: "70px",
            left: "120px",
            opacity: 0.6,
          })}
        >
          <Text size="xs">T</Text>
        </Avatar>
      </Box>

      <Title order={3} size="h4" mb="xs">
        Invite your first user
      </Title>
      <Text size="sm" c="#6B7280" mb="xl" maw={300}>
        Add your team members and external users.
      </Text>

      <Group gap="sm">
        <Button
          variant="outline"
          leftSection={<HiLink size={16} />}
          className={css({
            borderColor: "#E5E7EB",
            color: "#6B7280",
            "&:hover": {
              backgroundColor: "#F9FAFB",
            },
          })}
        >
          Create invite link
        </Button>

        <Button
          className={css({
            backgroundColor: "#1F2937",
            color: "white",
            "&:hover": {
              backgroundColor: "#111827",
            },
          })}
          leftSection={<HiPlus size={16} />}
        >
          Add user
        </Button>
      </Group>
    </Box>
  );
}

function UserList({ users }: { users: typeof mockUsers }) {
  const getAvatarColor = (index: number) => {
    const colors = ["blue", "green", "orange", "purple", "pink", "cyan", "teal", "indigo"];
    return colors[index % colors.length];
  };

  return (
    <Box
      className={css({
        backgroundColor: "white",
        border: "1px solid #E5E7EB",
        borderRadius: "12px",
        overflow: "hidden",
      })}
    >
      {users.map((user, index) => (
        <Box
          key={user.id}
          className={css({
            padding: "16px 20px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            borderBottom: index < users.length - 1 ? "1px solid #F3F4F6" : "none",
            "&:hover": {
              backgroundColor: "#FAFBFC",
            },
          })}
        >
          <Group gap="md">
            <Avatar size="md" color={getAvatarColor(index)}>
              <Text size="sm" fw={500}>
                {user.name.split(' ').map(n => n[0]).join('')}
              </Text>
            </Avatar>
            <Box>
              <Text fw={500} size="sm" c="#1F2937">{user.name}</Text>
              <Text size="xs" c="#6B7280">{user.email}</Text>
            </Box>
          </Group>

          <Group gap="sm">
            <Badge
              variant="light"
              color={user.role === "Admin" ? "red" : user.role === "Editor" ? "blue" : "gray"}
              size="sm"
              className={css({
                textTransform: "none",
                fontWeight: 500,
              })}
            >
              {user.role}
            </Badge>
            <ActionIcon variant="subtle" color="gray" size="sm">
              <Text size="sm" c="#6B7280">⋯</Text>
            </ActionIcon>
          </Group>
        </Box>
      ))}
    </Box>
  );
}
