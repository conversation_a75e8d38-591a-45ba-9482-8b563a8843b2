import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { Container, Center, Loader, Text, Alert, Button } from "@mantine/core";
import { useEffect, useState } from "react";
import { css } from "../../../styled-system/css";

export const Route = createFileRoute("/auth/facebook/callback")({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => ({
    code: (search.code as string) || "",
    error: (search.error as string) || "",
    error_description: (search.error_description as string) || "",
    state: (search.state as string) || "",
  }),
});

function RouteComponent() {
  const navigate = useNavigate();
  const { code, error, error_description } = Route.useSearch();
  const [isProcessing, setIsProcessing] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);

  useEffect(() => {
    const handleFacebookCallback = async () => {
      try {
        if (error) {
          throw new Error(`Facebook OAuth error: ${error_description || error}`);
        }

        if (!code) {
          throw new Error("No authorization code received from Facebook");
        }

        // TODO: Exchange code for tokens with your backend
        console.log("Facebook auth code:", code);
        
        // Simulate API call to exchange code for tokens
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // TODO: Store tokens and user data
        // setAuthToken(response.access_token);
        
        // Redirect to dashboard or intended page
        navigate({ to: "/" });
        
      } catch (err) {
        console.error("Facebook OAuth callback error:", err);
        setAuthError(err instanceof Error ? err.message : "Authentication failed");
        setIsProcessing(false);
      }
    };

    handleFacebookCallback();
  }, [code, error, error_description, navigate]);

  if (authError) {
    return (
      <Container size={420} my={40}>
        <Center>
          <div className={css({ width: "100%", maxWidth: "420px" })}>
            <Alert color="red" variant="light" mb="xl">
              <Text size="sm" fw={500} mb="xs">
                Authentication Failed
              </Text>
              <Text size="sm">{authError}</Text>
            </Alert>

            <Button
              fullWidth
              onClick={() => navigate({ to: "/account/login" })}
              className={css({
                backgroundColor: "blue.600",
                _hover: { backgroundColor: "blue.700" },
              })}
            >
              Back to Login
            </Button>
          </div>
        </Center>
      </Container>
    );
  }

  return (
    <Container size={420} my={40}>
      <Center>
        <div className={css({ textAlign: "center" })}>
          <Loader size="lg" mb="xl" />
          <Text size="lg" fw={500} mb="xs">
            Completing Facebook Sign In
          </Text>
          <Text size="sm" c="dimmed">
            Please wait while we authenticate your account...
          </Text>
        </div>
      </Center>
    </Container>
  );
}
