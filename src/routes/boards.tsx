import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import { Box, Loader, Center, Text, Container, Title, Button } from "@mantine/core";
import { useEffect, useState } from "react";
import { HiHome } from "react-icons/hi";
import { useKanbanStore } from "@/stores/kanbanStore";
import { KanbanBoard } from "@/components/kanban/KanbanBoard";
import { Sidebar } from "@/components/layout/Sidebar";
import { Header } from "@/components/layout/Header";
import { css } from "styled-system/css";

export const Route = createFileRoute("/boards")({
  component: RouteComponent,
});

function RouteComponent() {
  const { board, initializeWithSampleData } = useKanbanStore();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    if (!board) {
      initializeWithSampleData();
    }
  }, [board, initializeWithSampleData]);

  return (
    <Box
      className={css({
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#FAFBFC",
      })}
    >
      {/* Sidebar */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content */}
      <Box
        className={css({
          flex: 1,
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        })}
      >
        {/* Header */}
        <Header />

        {/* Board Content */}
        <Box
          className={css({
            flex: 1,
            padding: "24px",
            backgroundColor: "#FFFFFF",
            overflow: "hidden",
          })}
        >
          {board ? (
            <KanbanBoard board={board} />
          ) : (
            <Center
              className={css({
                height: "400px",
              })}
            >
              <Box className={css({ textAlign: "center" })}>
                <Loader size="lg" color="#3B82F6" mb="md" />
                <Text
                  className={css({
                    color: "#6B7280",
                    fontSize: "14px",
                  })}
                >
                  Loading your board...
                </Text>
              </Box>
            </Center>
          )}
        </Box>
      </Box>
    </Box>
  );
}
