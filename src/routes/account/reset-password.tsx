import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, useSearch } from "@tanstack/react-router";
import {
  PasswordInput,
  Button,
  Title,
  Text,
  Anchor,
  Stack,
  Container,
  Center,
  Alert,
  Box,
  Group,
} from "@mantine/core";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { FaCheckCircle } from "react-icons/fa";
import { css } from "styled-system/css";

export const Route = createFileRoute("/account/reset-password")({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => ({
    token: (search.token as string) || "",
  }),
});

interface ResetPasswordForm {
  password: string;
  confirmPassword: string;
}

function RouteComponent() {
  const { token } = useSearch({ from: "/account/reset-password" });
  const [isPasswordReset, setIsPasswordReset] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ResetPasswordForm>();

  const password = watch("password");

  const onSubmit = async (data: ResetPasswordForm) => {
    try {
      setError(null);
      // TODO: Implement reset password logic
      console.log("Reset password data:", { ...data, token });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate token validation
      if (!token) {
        throw new Error("Invalid or expired reset token");
      }
      
      setIsPasswordReset(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    }
  };

  // Show error if no token provided
  if (!token) {
    return (
      <div
        className={css({
          minHeight: "100vh",
          background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px",
        })}
      >
        <Container size="sm">
          <Center>
            <Box
              className={css({
                background: "white",
                borderRadius: "24px",
                padding: "60px 40px",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                maxWidth: "500px",
                width: "100%",
              })}
            >
              <Alert color="red" variant="light" mb="xl">
                <Text size="sm">
                  Invalid or missing reset token. Please request a new password reset link.
                </Text>
              </Alert>

              <Button
                fullWidth
                component={Link}
                to="/account/forgot-password"
                size="md"
                className={css({
                  background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                  border: "none",
                  color: "white",
                  height: "48px",
                  _hover: { opacity: 0.9 },
                })}
              >
                Request new reset link
              </Button>
            </Box>
          </Center>
        </Container>
      </div>
    );
  }

  if (isPasswordReset) {
    return (
      <div
        className={css({
          minHeight: "100vh",
          background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px",
        })}
      >
        <Container size="sm">
          <Center>
            <Box
              className={css({
                background: "white",
                borderRadius: "24px",
                padding: "60px 40px",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                maxWidth: "500px",
                width: "100%",
                textAlign: "center",
              })}
            >
              <Center mb="xl">
                <FaCheckCircle
                  size={48}
                  className={css({ color: "#10b981" })}
                />
              </Center>
              
              <Title
                ta="center"
                className={css({
                  fontSize: "28px",
                  fontWeight: 700,
                  marginBottom: "16px",
                  color: "gray.900",
                })}
              >
                Password reset successful
              </Title>

              <Text c="gray.6" size="sm" ta="center" mb="xl">
                Your password has been successfully reset. You can now sign in with your new password.
              </Text>

              <Button
                fullWidth
                component={Link}
                to="/account/login"
                size="md"
                className={css({
                  background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                  border: "none",
                  color: "white",
                  height: "48px",
                  _hover: { opacity: 0.9 },
                })}
              >
                Sign in
              </Button>
            </Box>
          </Center>
        </Container>
      </div>
    );
  }

  return (
    <div
      className={css({
        minHeight: "100vh",
        background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "20px",
      })}
    >
      <Container size="sm">
        <Center>
          <Box
            className={css({
              background: "white",
              borderRadius: "24px",
              padding: "60px 40px",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              maxWidth: "500px",
              width: "100%",
            })}
          >
            {/* Logo */}
            <Group mb="xl" justify="center">
              <div
                className={css({
                  width: "32px",
                  height: "32px",
                  background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                  borderRadius: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                })}
              >
                <Text c="white" fw={700} size="lg">
                  R
                </Text>
              </div>
              <Text fw={600} size="xl" c="gray.8">
                Recova
              </Text>
            </Group>

            <Title
              ta="center"
              className={css({
                fontSize: "28px",
                fontWeight: 700,
                marginBottom: "16px",
                color: "gray.900",
              })}
            >
              Reset your password
            </Title>

            <Text c="gray.6" size="sm" ta="center" mb="xl">
              Enter your new password below.
            </Text>

            {error && (
              <Alert color="red" variant="light" mb="md">
                <Text size="sm">{error}</Text>
              </Alert>
            )}

            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack gap="md">
                <PasswordInput
                  placeholder="Enter your new password"
                  size="md"
                  error={errors.password?.message}
                  className={css({
                    "& input": {
                      height: "48px",
                      border: "1px solid #e9ecef",
                      _focus: { borderColor: "#ff6b6b" },
                    },
                  })}
                  {...register("password", {
                    required: "Password is required",
                    minLength: {
                      value: 8,
                      message: "Password must be at least 8 characters",
                    },
                    pattern: {
                      value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                      message: "Password must contain uppercase, lowercase, and number",
                    },
                  })}
                />

                <PasswordInput
                  placeholder="Confirm your new password"
                  size="md"
                  error={errors.confirmPassword?.message}
                  className={css({
                    "& input": {
                      height: "48px",
                      border: "1px solid #e9ecef",
                      _focus: { borderColor: "#ff6b6b" },
                    },
                  })}
                  {...register("confirmPassword", {
                    required: "Please confirm your password",
                    validate: (value) =>
                      value === password || "Passwords do not match",
                  })}
                />

                <Button
                  type="submit"
                  fullWidth
                  size="md"
                  loading={isSubmitting}
                  className={css({
                    background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                    border: "none",
                    color: "white",
                    height: "48px",
                    _hover: { opacity: 0.9 },
                  })}
                >
                  Reset password
                </Button>
              </Stack>
            </form>

            <Text c="gray.6" size="sm" ta="center" mt="lg">
              Remember your password?{" "}
              <Anchor
                component={Link}
                to="/account/login"
                className={css({ color: "#ff6b6b", textDecoration: "none" })}
              >
                Back to login
              </Anchor>
            </Text>
          </Box>
        </Center>
      </Container>
    </div>
  );
}