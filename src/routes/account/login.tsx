import { createF<PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { TextInput, Button, Title, Text, Anchor, Stack, Group, Container, Center, Box } from "@mantine/core";
import { useForm } from "react-hook-form";
import { FcGoogle } from "react-icons/fc";
import { SiFacebook } from "react-icons/si";
import { MdEmail } from "react-icons/md";
import { css } from "styled-system/css";

export const Route = createFileRoute("/account/login")({
  component: RouteComponent,
});

interface LoginForm {
  email: string;
}

function RouteComponent() {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginForm>();

  const onSubmit = async (data: LoginForm) => {
    // TODO: Implement email login logic
    console.log("Email login data:", data);
  };

  const handleGoogleLogin = () => {
    // TODO: Implement Google OAuth
    console.log("Google login");
  };

  const handleMicrosoftLogin = () => {
    // TODO: Implement Microsoft OAuth
    console.log("Microsoft login");
  };

  return (
    <div
      className={css({
        minHeight: "100vh",
        background: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "20px",
      })}
    >
      <Container size="lg">
        <Center>
          <div
            className={css({
              display: "flex",
              background: "white",
              borderRadius: "24px",
              overflow: "hidden",
              boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
              maxWidth: "900px",
              width: "100%",
              minHeight: "600px",
            })}
          >
            {/* Left side - Image */}
            <Box
              className={css({
                flex: 1,
                animation: "gradient 15s ease infinite",
                position: "relative",
                w: "96",
                display: { base: "none", md: "block" },
              })}
            >
              <div
                className={css({
                  position: "absolute",
                  inset: 0,
                })}
                style={{
                  backgroundImage:
                    "url(https://artbusinessnews.com/wp-content/uploads/2022/06/pexels-steve-johnson-1269968-scaled.jpg)",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              />
            </Box>

            {/* Right side - Form */}
            <Box
              className={css({
                flex: 1,
                padding: "60px 40px",
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                minWidth: "400px",
              })}
            >
              {/* Logo */}
              <Group mb="xl" justify="center">
                <div
                  className={css({
                    width: "32px",
                    height: "32px",
                    background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                    borderRadius: "8px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  })}
                >
                  <Text c="white" fw={700} size="lg">
                    P
                  </Text>
                </div>
                <Text fw={600} size="xl" c="gray.8">
                  PinkDesign
                </Text>
              </Group>

              <Title
                ta="center"
                className={css({
                  fontSize: "lg",
                  fontWeight: 700,
                  marginBottom: "8px",
                  color: "gray.900",
                })}
              >
                Welcome back <i>!</i>
              </Title>

              <Text
                ta="center"
                c="gray.6"
                size="sm"
                mb="xl"
                className={css({
                  lineHeight: 1.5,
                  maxWidth: "320px",
                  margin: "0 auto 32px",
                })}
              ></Text>

              <Stack gap="md">
                <Button
                  variant="default"
                  leftSection={<FcGoogle color="#4285f4" />}
                  onClick={handleGoogleLogin}
                  size="md"
                  className={css({
                    border: "1px solid #e9ecef",
                    backgroundColor: "white",
                    color: "gray.700",
                    _hover: { backgroundColor: "gray.50" },
                    height: "48px",
                  })}
                >
                  Continue with Google
                </Button>

                <Button
                  variant="default"
                  leftSection={<SiFacebook color="#00a1f1" />}
                  onClick={handleMicrosoftLogin}
                  size="md"
                  className={css({
                    border: "1px solid #e9ecef",
                    backgroundColor: "white",
                    color: "gray.700",
                    _hover: { backgroundColor: "gray.50" },
                    height: "48px",
                  })}
                >
                  Continue with Facebook
                </Button>

                <Text ta="center" c="gray.5" size="sm" my="md">
                  Or
                </Text>

                <form onSubmit={handleSubmit(onSubmit)}>
                  <Stack gap="md">
                    <TextInput
                      leftSection={<MdEmail color="#9ca3af" />}
                      placeholder="<EMAIL>"
                      size="md"
                      error={errors.email?.message}
                      className={css({
                        "& input": {
                          height: "48px",
                          border: "1px solid #e9ecef",
                          _focus: { borderColor: "#ff6b6b" },
                        },
                      })}
                      {...register("email", {
                        required: "Email is required",
                        pattern: {
                          value: /^\S+@\S+$/i,
                          message: "Invalid email address",
                        },
                      })}
                    />

                    <Button
                      type="submit"
                      size="md"
                      loading={isSubmitting}
                      className={css({
                        background: "linear-gradient(45deg, #ff6b6b, #ee5a24)",
                        border: "none",
                        color: "white",
                        height: "48px",
                        _hover: { opacity: 0.9 },
                      })}
                    >
                      Continue with email
                    </Button>
                  </Stack>
                </form>

                <Text ta="center" c="gray.6" size="sm" mt="lg">
                  Do not have an account?{" "}
                  <Anchor
                    component={Link}
                    to="/account/signup"
                    className={css({
                      color: "#ff6b6b",
                      textDecoration: "none",
                    })}
                  >
                    Sign up
                  </Anchor>
                </Text>

                <Text ta="center" c="gray.5" size="xs" mt="xl">
                  By signing up, you agree to our{" "}
                  <Anchor href="#" c="gray.7" size="xs">
                    Terms of services
                  </Anchor>{" "}
                  &{" "}
                  <Anchor href="#" c="gray.7" size="xs">
                    Privacy policy
                  </Anchor>
                </Text>
              </Stack>
            </Box>
          </div>
        </Center>
      </Container>
    </div>
  );
}
