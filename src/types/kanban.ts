export type Priority = 'low' | 'medium' | 'high' | 'urgent';

export type CardStatus = 'todo' | 'in-progress' | 'review' | 'done';

export interface KanbanCard {
  id: string;
  title: string;
  description?: string;
  priority: Priority;
  assignee?: string;
  tags?: string[];
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface KanbanColumn {
  id: string;
  title: string;
  status: CardStatus;
  cards: KanbanCard[];
  color?: string;
  limit?: number; // WIP limit
}

export interface KanbanBoard {
  id: string;
  title: string;
  description?: string;
  columns: KanbanColumn[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DragEndEvent {
  active: {
    id: string;
    data: {
      current?: {
        type: 'card' | 'column';
        card?: KanbanCard;
        column?: KanbanColumn;
      };
    };
  };
  over: {
    id: string;
    data: {
      current?: {
        type: 'card' | 'column';
        accepts?: string[];
        column?: KanbanColumn;
      };
    };
  } | null;
}

export interface KanbanActions {
  moveCard: (cardId: string, fromColumnId: string, toColumnId: string, newIndex?: number) => void;
  addCard: (columnId: string, card: Omit<KanbanCard, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCard: (cardId: string, updates: Partial<KanbanCard>) => void;
  deleteCard: (cardId: string, columnId: string) => void;
  addColumn: (column: Omit<KanbanColumn, 'id' | 'cards'>) => void;
  updateColumn: (columnId: string, updates: Partial<KanbanColumn>) => void;
  deleteColumn: (columnId: string) => void;
  reorderColumns: (fromIndex: number, toIndex: number) => void;
}
