import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { KanbanBoard, KanbanColumn, KanbanCard, KanbanActions } from '../types/kanban';

interface KanbanStore {
  board: KanbanBoard | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setBoard: (board: KanbanBoard) => void;
  moveCard: (cardId: string, fromColumnId: string, toColumnId: string, newIndex?: number) => void;
  reorderCards: (columnId: string, fromIndex: number, toIndex: number) => void;
  addCard: (columnId: string, card: Omit<KanbanCard, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCard: (cardId: string, updates: Partial<KanbanCard>) => void;
  deleteCard: (cardId: string, columnId: string) => void;
  addColumn: (column: Omit<KanbanColumn, 'id' | 'cards'>) => void;
  updateColumn: (columnId: string, updates: Partial<KanbanColumn>) => void;
  deleteColumn: (columnId: string) => void;
  reorderColumns: (fromIndex: number, toIndex: number) => void;
  initializeWithSampleData: () => void;
}

const generateId = () => Math.random().toString(36).substr(2, 9);

const createSampleData = (): KanbanBoard => ({
  id: 'board-1',
  title: 'Project Dashboard',
  description: 'Main project kanban board',
  createdAt: new Date(),
  updatedAt: new Date(),
  columns: [
    {
      id: 'col-todo',
      title: 'To Do',
      status: 'todo',
      color: '#e3f2fd',
      cards: [
        {
          id: 'card-1',
          title: 'Design new landing page',
          description: 'Create wireframes and mockups for the new landing page',
          priority: 'high',
          assignee: 'John Doe',
          tags: ['design', 'ui/ux'],
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'card-2',
          title: 'Set up CI/CD pipeline',
          description: 'Configure automated testing and deployment',
          priority: 'medium',
          assignee: 'Jane Smith',
          tags: ['devops', 'automation'],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    },
    {
      id: 'col-progress',
      title: 'In Progress',
      status: 'in-progress',
      color: '#fff3e0',
      cards: [
        {
          id: 'card-3',
          title: 'Implement user authentication',
          description: 'Add login, signup, and password reset functionality',
          priority: 'high',
          assignee: 'Mike Johnson',
          tags: ['backend', 'security'],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    },
    {
      id: 'col-review',
      title: 'Review',
      status: 'review',
      color: '#f3e5f5',
      cards: [
        {
          id: 'card-4',
          title: 'Update documentation',
          description: 'Review and update API documentation',
          priority: 'low',
          assignee: 'Sarah Wilson',
          tags: ['documentation'],
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
    },
    {
      id: 'col-done',
      title: 'Done',
      status: 'done',
      color: '#e8f5e8',
      cards: [
        {
          id: 'card-5',
          title: 'Setup project structure',
          description: 'Initialize React project with TypeScript and required dependencies',
          priority: 'high',
          assignee: 'John Doe',
          tags: ['setup', 'frontend'],
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          updatedAt: new Date(),
        },
      ],
    },
  ],
});

export const useKanbanStore = create<KanbanStore>()(
  devtools(
    (set, get) => ({
      board: null,
      isLoading: false,
      error: null,

      setBoard: (board) => set({ board }),

      moveCard: (cardId, fromColumnId, toColumnId, newIndex) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const fromColumn = newBoard.columns.find(col => col.id === fromColumnId);
        const toColumn = newBoard.columns.find(col => col.id === toColumnId);

        if (!fromColumn || !toColumn) return;

        const cardIndex = fromColumn.cards.findIndex(card => card.id === cardId);
        if (cardIndex === -1) return;

        const [card] = fromColumn.cards.splice(cardIndex, 1);
        
        if (newIndex !== undefined) {
          toColumn.cards.splice(newIndex, 0, card);
        } else {
          toColumn.cards.push(card);
        }

        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      reorderCards: (columnId, fromIndex, toIndex) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const column = newBoard.columns.find(col => col.id === columnId);
        if (!column) return;

        const [movedCard] = column.cards.splice(fromIndex, 1);
        column.cards.splice(toIndex, 0, movedCard);

        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      addCard: (columnId, cardData) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const column = newBoard.columns.find(col => col.id === columnId);
        if (!column) return;

        const newCard: KanbanCard = {
          ...cardData,
          id: generateId(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        column.cards.push(newCard);
        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      updateCard: (cardId, updates) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        for (const column of newBoard.columns) {
          const card = column.cards.find(c => c.id === cardId);
          if (card) {
            Object.assign(card, updates, { updatedAt: new Date() });
            newBoard.updatedAt = new Date();
            set({ board: newBoard });
            return;
          }
        }
      },

      deleteCard: (cardId, columnId) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const column = newBoard.columns.find(col => col.id === columnId);
        if (!column) return;

        column.cards = column.cards.filter(card => card.id !== cardId);
        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      addColumn: (columnData) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const newColumn: KanbanColumn = {
          ...columnData,
          id: generateId(),
          cards: [],
        };

        newBoard.columns.push(newColumn);
        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      updateColumn: (columnId, updates) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const column = newBoard.columns.find(col => col.id === columnId);
        if (!column) return;

        Object.assign(column, updates);
        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      deleteColumn: (columnId) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        newBoard.columns = newBoard.columns.filter(col => col.id !== columnId);
        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      reorderColumns: (fromIndex, toIndex) => {
        const { board } = get();
        if (!board) return;

        const newBoard = { ...board };
        const [column] = newBoard.columns.splice(fromIndex, 1);
        newBoard.columns.splice(toIndex, 0, column);
        newBoard.updatedAt = new Date();
        set({ board: newBoard });
      },

      initializeWithSampleData: () => {
        const sampleBoard = createSampleData();
        set({ board: sampleBoard });
      },
    }),
    {
      name: 'kanban-store',
    }
  )
);
