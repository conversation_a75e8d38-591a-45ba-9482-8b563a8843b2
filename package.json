{"name": "pinktool-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mantine/core": "^8.2.1", "@mantine/hooks": "^8.2.1", "@metamask/jazzicon": "^2.0.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.129.8", "lodash-es": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@hey-api/openapi-ts": "^0.80.1", "@pandacss/dev": "^0.54.0", "@tanstack/router-plugin": "^1.129.8", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-tsconfig-paths": "^5.1.4"}}